import { defineS<PERSON> } from 'pinia'
import type { SpeechVoice } from '~/composables/useSpeechVoices'

interface DialogItem {
  speakerIndex: number
  input: string
}

interface SpeakerItem {
  name: string
  voice: {
    id: string
    name: string
  }
}

export const useDialogToSpeechStore = defineStore('dialogToSpeechStore', {
  state: () => ({
    dialogs: [{
      speakerIndex: 0,
      input: ''
    }] as DialogItem[],
    speakers: [] as SpeakerItem[],

    // Voice settings for Voice 1 and Voice 2
    voice1: null as SpeechVoice | null,
    voice2: null as SpeechVoice | null,

    loadings: {
      generateSpeech: false
    } as Record<string, boolean>,

    errors: {
      generateSpeech: null
    } as Record<string, any>
  }),

  getters: {
    hasDialogs: state => state.dialogs.length > 0,
    hasSpeakers: state => state.speakers.length > 0,
    getSpeakerByIndex: state => (index: number) => {
      return state.speakers[index] || null
    },
    getVoiceByIndex: state => (index: number) => {
      if (index === 0) return state.voice1
      if (index === 1) return state.voice2
      return null
    }
  },

  actions: {
    // Voice management actions
    setVoice1(voice: SpeechVoice | null) {
      this.voice1 = voice
    },

    setVoice2(voice: SpeechVoice | null) {
      this.voice2 = voice
    },

    // Speaker management actions
    addSpeaker(speaker: SpeakerItem) {
      this.speakers.push(speaker)
    },

    removeSpeaker(index: number) {
      if (index >= 0 && index < this.speakers.length) {
        this.speakers.splice(index, 1)
        // Update dialog speaker indices if needed
        this.dialogs.forEach((dialog) => {
          if (dialog.speakerIndex > index) {
            dialog.speakerIndex -= 1
          } else if (dialog.speakerIndex === index) {
            // Reset to first speaker or -1 if no speakers
            dialog.speakerIndex = this.speakers.length > 0 ? 0 : -1
          }
        })
      }
    },

    updateSpeaker(index: number, speaker: SpeakerItem) {
      if (index >= 0 && index < this.speakers.length) {
        this.speakers[index] = speaker
      }
    },

    // Dialog management actions
    addDialog(dialog?: DialogItem) {
      const lastDialog = this.dialogs[this.dialogs.length - 1]
      const newDialog = dialog || {
        speakerIndex: lastDialog?.speakerIndex === 0 ? 1 : 0,
        input: ''
      }
      this.dialogs.push(newDialog)
    },

    removeDialog(index: number) {
      if (index >= 0 && index < this.dialogs.length) {
        this.dialogs.splice(index, 1)
      }
    },

    updateDialog(index: number, dialog: DialogItem) {
      if (index >= 0 && index < this.dialogs.length) {
        this.dialogs[index] = dialog
      }
    },

    clearDialogs() {
      this.dialogs = []
    },

    clearSpeakers() {
      this.speakers = []
    },

    clearAll() {
      this.dialogs = []
      this.speakers = []
    },

    // Generate speech for all dialogs
    async generateDialogSpeech(payload: {
      model: string
      emotion?: string
      speed?: number
      output_format?: string
      output_channel?: string
      custom_prompt?: string
      vibe_id?: number
      accent?: string
      model_name?: string
      name?: string
    }) {
      try {
        this.loadings.generateSpeech = true
        this.errors.generateSpeech = null

        // Convert dialogs and speakers to the format expected by textToSpeech
        const voices = this.dialogs.map((dialog) => {
          const voice = this.getVoiceByIndex(dialog.speakerIndex)
          return {
            name: voice?.speaker_name || `Voice ${dialog.speakerIndex + 1}`,
            voice: {
              id: voice?.id || '',
              name: voice?.speaker_name || ''
            },
            input: dialog.input
          }
        })

        // Combine all dialog inputs
        const combinedInput = this.dialogs.map((dialog) => {
          const voice = this.getVoiceByIndex(dialog.speakerIndex)
          const voiceName = voice?.speaker_name || `Voice ${dialog.speakerIndex + 1}`
          return `${voiceName}: ${dialog.input}`
        }).join('\n\n')

        // Return the data to be used by the calling component
        return {
          input: combinedInput,
          voices,
          ...payload
        }
      } catch (error) {
        console.error('Dialog to speech generation failed:', error)
        this.errors.generateSpeech = error
        return null
      } finally {
        this.loadings.generateSpeech = false
      }
    }
  }
})
